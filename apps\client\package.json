{"name": "@rxy/client", "version": "1.0.0", "description": "Client website for RxY platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.0.7", "@rxy/shared": "^1.0.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^14.0.4", "next-themes": "^0.2.1", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-intersection-observer": "^9.5.3", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.3.0"}}