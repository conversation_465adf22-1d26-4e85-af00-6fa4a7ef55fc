{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "baseUrl": ".", "paths": {"@rxy/shared/*": ["../../packages/shared/src/*"], "@rxy/database/*": ["../../packages/database/src/*"], "@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}