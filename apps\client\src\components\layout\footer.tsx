'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Github, 
  Linkedin, 
  Twitter,
  Heart,
  ArrowUp,
  Code,
  Coffee
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const navigation = {
  main: [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Projects', href: '#projects' },
    { name: 'Blog', href: '#blog' },
    { name: 'Contact', href: '#contact' },
  ],
  services: [
    { name: 'Web Development', href: '/services/web-development' },
    { name: 'Mobile Apps', href: '/services/mobile-apps' },
    { name: 'API Development', href: '/services/api-development' },
    { name: 'Consulting', href: '/services/consulting' },
    { name: 'Code Review', href: '/services/code-review' },
  ],
  resources: [
    { name: 'Blog', href: '/blog' },
    { name: 'Case Studies', href: '/case-studies' },
    { name: 'Tech Stack', href: '/tech-stack' },
    { name: 'Open Source', href: '/open-source' },
    { name: 'Newsletter', href: '/newsletter' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
  ],
};

const socialLinks = [
  {
    name: 'GitHub',
    href: 'https://github.com/rxydev',
    icon: Github,
    description: 'View my code and contributions'
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/in/rxydev',
    icon: Linkedin,
    description: 'Connect professionally'
  },
  {
    name: 'Twitter',
    href: 'https://twitter.com/rxydev',
    icon: Twitter,
    description: 'Follow for tech updates'
  },
];

const contactInfo = [
  {
    icon: Mail,
    label: 'Email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: MapPin,
    label: 'Location',
    value: 'Remote Worldwide',
    href: null
  },
];

export function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.querySelector(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-muted/30 border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16 lg:py-20">
          <div className="grid lg:grid-cols-4 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Link
                  href="/"
                  className="text-2xl font-bold text-foreground hover:text-primary transition-colors"
                >
                  RxY<span className="text-primary">.dev</span>
                </Link>
                
                <p className="mt-4 text-muted-foreground leading-relaxed">
                  Crafting exceptional digital experiences with modern technologies. 
                  Transforming ideas into scalable, user-focused solutions.
                </p>

                {/* Contact Info */}
                <div className="mt-6 space-y-3">
                  {contactInfo.map((contact) => (
                    <div key={contact.label} className="flex items-center text-sm">
                      <contact.icon className="w-4 h-4 mr-3 text-primary" />
                      {contact.href ? (
                        <a 
                          href={contact.href}
                          className="text-muted-foreground hover:text-foreground transition-colors"
                        >
                          {contact.value}
                        </a>
                      ) : (
                        <span className="text-muted-foreground">{contact.value}</span>
                      )}
                    </div>
                  ))}
                </div>

                {/* Availability Status */}
                <div className="mt-6 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                    <span className="text-sm font-medium text-green-800 dark:text-green-200">
                      Available for new projects
                    </span>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Navigation Links */}
            <div className="lg:col-span-2 grid sm:grid-cols-3 gap-8">
              {/* Main Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                  Navigation
                </h3>
                <ul className="space-y-3">
                  {navigation.main.map((item) => (
                    <li key={item.name}>
                      <button
                        onClick={() => scrollToSection(item.href)}
                        className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                      >
                        {item.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Services */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                  Services
                </h3>
                <ul className="space-y-3">
                  {navigation.services.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Resources */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                  Resources
                </h3>
                <ul className="space-y-3">
                  {navigation.resources.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Social & Newsletter */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                {/* Social Links */}
                <div>
                  <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                    Connect
                  </h3>
                  <div className="flex space-x-3">
                    {socialLinks.map((social) => (
                      <Button
                        key={social.name}
                        variant="outline"
                        size="icon"
                        asChild
                        className="hover:bg-primary hover:text-primary-foreground hover:border-primary transition-colors"
                      >
                        <a
                          href={social.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label={social.name}
                          title={social.description}
                        >
                          <social.icon className="w-4 h-4" />
                        </a>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Newsletter Signup */}
                <div>
                  <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                    Stay Updated
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Get the latest insights on web development and tech trends.
                  </p>
                  <Button
                    variant="default"
                    className="w-full"
                    onClick={() => scrollToSection('#contact')}
                  >
                    Subscribe to Newsletter
                  </Button>
                </div>

                {/* Tech Stack Badge */}
                <div className="pt-4">
                  <div className="flex items-center text-xs text-muted-foreground mb-2">
                    <Code className="w-3 h-3 mr-1" />
                    Built with
                  </div>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">Next.js</Badge>
                    <Badge variant="secondary" className="text-xs">TypeScript</Badge>
                    <Badge variant="secondary" className="text-xs">Tailwind</Badge>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-border py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="flex items-center text-sm text-muted-foreground"
            >
              <span>© {new Date().getFullYear()} RxY.dev. Made with</span>
              <Heart className="w-4 h-4 mx-1 text-red-500 fill-current" />
              <span>and</span>
              <Coffee className="w-4 h-4 mx-1 text-amber-600" />
              <span>by RxY Developer</span>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="flex items-center space-x-6"
            >
              {/* Legal Links */}
              <div className="flex items-center space-x-4">
                {navigation.legal.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {item.name}
                  </Link>
                ))}
              </div>

              {/* Back to Top */}
              <Button
                variant="ghost"
                size="icon"
                onClick={scrollToTop}
                className="hover:bg-primary hover:text-primary-foreground transition-colors"
                aria-label="Back to top"
              >
                <ArrowUp className="w-4 h-4" />
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
}
