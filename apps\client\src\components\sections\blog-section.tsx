'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Calendar, Clock, ArrowRight, TrendingUp, BookOpen, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

// Mock blog posts - will be fetched from API in production
const blogPosts = [
  {
    id: 1,
    title: 'Building Scalable React Applications: Best Practices for 2024',
    excerpt: 'Discover the latest patterns and techniques for building maintainable React applications that scale with your business needs.',
    content: 'Learn about component architecture, state management, performance optimization, and testing strategies...',
    slug: 'building-scalable-react-applications-2024',
    category: 'React',
    tags: ['React', 'JavaScript', 'Performance', 'Architecture'],
    publishedAt: '2024-01-15',
    readTime: 8,
    views: 12500,
    likes: 245,
    featured: true,
    image: '/images/blog/react-scalability.jpg', // Admin will upload
    author: {
      name: '<PERSON><PERSON><PERSON> Developer',
      avatar: '/images/author-avatar.jpg',
    },
  },
  {
    id: 2,
    title: 'The Future of Web Development: AI-Powered Development Tools',
    excerpt: 'Explore how artificial intelligence is revolutionizing the way we build web applications and what it means for developers.',
    content: 'AI tools are changing the development landscape. From code generation to automated testing...',
    slug: 'future-web-development-ai-tools',
    category: 'AI/ML',
    tags: ['AI', 'Web Development', 'Tools', 'Future'],
    publishedAt: '2024-01-10',
    readTime: 6,
    views: 8900,
    likes: 189,
    featured: true,
    image: '/images/blog/ai-development.jpg', // Admin will upload
    author: {
      name: 'RxY Developer',
      avatar: '/images/author-avatar.jpg',
    },
  },
  {
    id: 3,
    title: 'Microservices Architecture: When and How to Implement',
    excerpt: 'A comprehensive guide to understanding microservices architecture, its benefits, challenges, and implementation strategies.',
    content: 'Microservices have become increasingly popular, but they\'re not always the right choice...',
    slug: 'microservices-architecture-guide',
    category: 'Architecture',
    tags: ['Microservices', 'Backend', 'Architecture', 'DevOps'],
    publishedAt: '2024-01-05',
    readTime: 12,
    views: 15200,
    likes: 312,
    featured: false,
    image: '/images/blog/microservices.jpg', // Admin will upload
    author: {
      name: 'RxY Developer',
      avatar: '/images/author-avatar.jpg',
    },
  },
  {
    id: 4,
    title: 'TypeScript Best Practices: Writing Better Code in 2024',
    excerpt: 'Master TypeScript with these essential best practices that will make your code more maintainable and bug-free.',
    content: 'TypeScript has become essential for modern web development. Here are the practices that matter...',
    slug: 'typescript-best-practices-2024',
    category: 'TypeScript',
    tags: ['TypeScript', 'JavaScript', 'Best Practices', 'Code Quality'],
    publishedAt: '2023-12-28',
    readTime: 10,
    views: 9800,
    likes: 201,
    featured: false,
    image: '/images/blog/typescript-practices.jpg', // Admin will upload
    author: {
      name: 'RxY Developer',
      avatar: '/images/author-avatar.jpg',
    },
  },
];

const categories = ['All', 'React', 'TypeScript', 'AI/ML', 'Architecture', 'DevOps'];

export function BlogSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const featuredPosts = blogPosts.filter(post => post.featured);
  const recentPosts = blogPosts.filter(post => !post.featured).slice(0, 2);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatViews = (views: number) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}k`;
    }
    return views.toString();
  };

  return (
    <section id="blog" className="py-20 lg:py-32 bg-muted/30" ref={ref}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge variant="secondary" className="mb-4">
            <BookOpen className="w-4 h-4 mr-2" />
            Tech Blog
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Latest Insights
            <span className="block text-primary">& Technical Articles</span>
          </h2>
          <p className="max-w-3xl mx-auto text-lg text-muted-foreground leading-relaxed">
            Sharing knowledge about modern web development, best practices, and emerging technologies. 
            Stay updated with the latest trends and techniques in the tech world.
          </p>
        </motion.div>

        {/* Featured Posts */}
        <div className="mb-16">
          <motion.h3
            initial={{ opacity: 0, x: -20 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-2xl font-bold text-foreground mb-8 flex items-center"
          >
            <TrendingUp className="w-6 h-6 mr-3 text-primary" />
            Featured Articles
          </motion.h3>

          <div className="grid lg:grid-cols-2 gap-8">
            {featuredPosts.map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              >
                <Card className="group h-full hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 overflow-hidden">
                  {/* Post Image */}
                  <div className="relative overflow-hidden bg-muted/30 h-48">
                    {/* Placeholder for admin-uploaded image */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2">
                          <BookOpen className="w-8 h-8 text-primary" />
                        </div>
                        <p className="text-sm text-muted-foreground font-medium">
                          {post.category}
                        </p>
                      </div>
                    </div>
                    
                    {/* Featured Badge */}
                    <div className="absolute top-4 left-4">
                      <Badge variant="default" className="bg-accent text-accent-foreground">
                        Featured
                      </Badge>
                    </div>
                  </div>

                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="secondary" className="text-xs">
                        {post.category}
                      </Badge>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(post.publishedAt)}
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="w-3 h-3 mr-1" />
                        {post.readTime} min read
                      </div>
                    </div>
                    
                    <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                      {post.title}
                    </CardTitle>
                    
                    <CardDescription className="text-muted-foreground mt-2 leading-relaxed line-clamp-3">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {post.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {post.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{post.tags.length - 3}
                        </Badge>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {formatViews(post.views)} views
                        </span>
                        <span>{post.likes} likes</span>
                      </div>
                    </div>

                    {/* Read More Button */}
                    <Button
                      variant="ghost"
                      className="w-full justify-between group-hover:bg-primary/10 transition-colors"
                      asChild
                    >
                      <a href={`/blog/${post.slug}`}>
                        Read Full Article
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Recent Posts */}
        <div className="mb-12">
          <motion.h3
            initial={{ opacity: 0, x: -20 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-2xl font-bold text-foreground mb-8"
          >
            Recent Posts
          </motion.h3>

          <div className="grid md:grid-cols-2 gap-6">
            {recentPosts.map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
              >
                <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 hover:border-primary/20">
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {post.category}
                      </Badge>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(post.publishedAt)}
                      </div>
                    </div>
                    
                    <CardTitle className="text-lg font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                      {post.title}
                    </CardTitle>
                    
                    <CardDescription className="text-muted-foreground leading-relaxed line-clamp-2">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>

                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.readTime} min read
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-primary hover:text-primary/80"
                        asChild
                      >
                        <a href={`/blog/${post.slug}`}>
                          Read More
                          <ArrowRight className="w-4 h-4 ml-1" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center"
        >
          <Button
            variant="outline"
            size="lg"
            className="px-8"
            asChild
          >
            <a href="/blog">
              View All Articles
              <ArrowRight className="w-4 h-4 ml-2" />
            </a>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
