{"name": "@rxy/api", "version": "1.0.0", "description": "API server for RxY platform", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@rxy/shared": "^1.0.0", "@rxy/database": "^1.0.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "openai": "^4.20.1", "dotenv": "^16.3.1", "cookie-parser": "^1.4.6", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/cookie-parser": "^1.4.6", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "rimraf": "^5.0.5"}}