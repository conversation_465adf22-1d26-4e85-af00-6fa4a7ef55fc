{"name": "rxy-platform", "version": "1.0.0", "description": "RxY.dev - A production-ready fullstack platform for thought leadership and portfolio showcase", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:api\"", "dev:client": "cd apps/client && npm run dev", "dev:admin": "cd apps/admin && npm run dev", "dev:api": "cd apps/api && npm run dev", "dev:full": "concurrently \"npm run dev:client\" \"npm run dev:admin\" \"npm run dev:api\"", "build": "npm run build:shared && npm run build:client && npm run build:api", "build:shared": "cd packages/shared && npm run build", "build:client": "cd apps/client && npm run build", "build:admin": "cd apps/admin && npm run build", "build:api": "cd apps/api && npm run build", "build:full": "npm run build:shared && npm run build:client && npm run build:admin && npm run build:api", "test": "npm run test:client && npm run test:api", "test:client": "cd apps/client && npm run test", "test:admin": "cd apps/admin && npm run test", "test:api": "cd apps/api && npm run test", "test:full": "npm run test:client && npm run test:admin && npm run test:api", "lint": "npm run lint:client && npm run lint:api", "lint:client": "cd apps/client && npm run lint", "lint:admin": "cd apps/admin && npm run lint", "lint:api": "cd apps/api && npm run lint", "lint:full": "npm run lint:client && npm run lint:admin && npm run lint:api", "clean": "rimraf apps/*/node_modules packages/*/node_modules node_modules", "setup": "npm install && npm run build:shared"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "author": "RxY Developer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/rxy-platform.git"}, "keywords": ["fullstack", "portfolio", "blog", "nextjs", "typescript", "mongodb", "express"]}