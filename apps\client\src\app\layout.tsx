import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { Toaster } from '@/components/ui/toaster';
import '@/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'RxY.dev - Fullstack Developer & Tech Innovator',
    template: '%s | RxY.dev',
  },
  description: 'Explore cutting-edge web development projects, insightful tech articles, and innovative solutions by a passionate fullstack developer.',
  keywords: [
    'fullstack developer',
    'web development',
    'react',
    'nextjs',
    'typescript',
    'nodejs',
    'mongodb',
    'tech blog',
    'portfolio',
    'software engineer',
  ],
  authors: [{ name: 'RxY Developer' }],
  creator: 'Rx<PERSON> Developer',
  publisher: 'RxY.dev',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://rxy.dev'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'RxY.dev - Fullstack Developer & Tech Innovator',
    description: 'Explore cutting-edge web development projects, insightful tech articles, and innovative solutions by a passionate fullstack developer.',
    siteName: 'RxY.dev',
    images: [
      {
        url: '/images/og-default.jpg',
        width: 1200,
        height: 630,
        alt: 'RxY.dev - Fullstack Developer & Tech Innovator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RxY.dev - Fullstack Developer & Tech Innovator',
    description: 'Explore cutting-edge web development projects, insightful tech articles, and innovative solutions by a passionate fullstack developer.',
    images: ['/images/og-default.jpg'],
    creator: '@rxydev',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
