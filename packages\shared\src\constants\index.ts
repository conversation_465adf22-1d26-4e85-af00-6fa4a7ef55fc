// API Configuration
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
  },
  BLOG: {
    POSTS: '/blog/posts',
    CATEGORIES: '/blog/categories',
    TAGS: '/blog/tags',
  },
  PROJECTS: {
    LIST: '/projects',
    CATEGORIES: '/projects/categories',
  },
  CONTACT: {
    MESSAGES: '/contact/messages',
    SEND: '/contact/send',
  },
  NEWSLETTER: {
    SUBSCRIBE: '/newsletter/subscribe',
    UNSUBSCRIBE: '/newsletter/unsubscribe',
    CAMPAIGNS: '/newsletter/campaigns',
    SUBSCRIBERS: '/newsletter/subscribers',
  },
  ANALYTICS: {
    PAGEVIEWS: '/analytics/pageviews',
    METRICS: '/analytics/metrics',
  },
  MEDIA: {
    UPLOAD: '/media/upload',
    DELETE: '/media/delete',
  },
  SETTINGS: {
    SITE: '/settings/site',
    USER: '/settings/user',
  },
  AI: {
    CONTENT_SUGGESTIONS: '/ai/content-suggestions',
    REPLY_SUGGESTIONS: '/ai/reply-suggestions',
    SEO_ANALYSIS: '/ai/seo-analysis',
  },
} as const;

// Application Configuration
export const APP_CONFIG = {
  CLIENT_URL: process.env.NEXT_PUBLIC_CLIENT_URL || 'http://localhost:3000',
  ADMIN_URL: process.env.NEXT_PUBLIC_ADMIN_URL || 'http://localhost:3001',
  API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002',
  
  // Pagination
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  
  // File Upload
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'text/plain'],
  
  // Content Limits
  MAX_BLOG_TITLE_LENGTH: 200,
  MAX_BLOG_EXCERPT_LENGTH: 500,
  MAX_PROJECT_TITLE_LENGTH: 100,
  MAX_PROJECT_DESCRIPTION_LENGTH: 500,
  MAX_CONTACT_MESSAGE_LENGTH: 5000,
  
  // SEO
  DEFAULT_META_TITLE: 'RxY.dev - Fullstack Developer & Tech Innovator',
  DEFAULT_META_DESCRIPTION: 'Explore cutting-edge web development projects, insightful tech articles, and innovative solutions by a passionate fullstack developer.',
  DEFAULT_OG_IMAGE: '/images/og-default.jpg',
  
  // Social Media
  SOCIAL_LINKS: {
    TWITTER: 'https://twitter.com/rxydev',
    LINKEDIN: 'https://linkedin.com/in/rxydev',
    GITHUB: 'https://github.com/rxydev',
    EMAIL: '<EMAIL>',
  },
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  COLORS: {
    PRIMARY: '#1a1a2e',      // Midnight Blue
    ACCENT: '#ff6b6b',       // Electric Coral
    BACKGROUND: '#f8f9fa',   // Soft Off-White
    SECONDARY: '#87ceeb',    // Pale Sky Blue
    TEXT: '#2c2c2c',         // Charcoal Black
    MUTED: '#6c757d',        // Muted Gray
    SUCCESS: '#28a745',      // Success Green
    WARNING: '#ffc107',      // Warning Yellow
    ERROR: '#dc3545',        // Error Red
  },
  
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
    '2XL': '1536px',
  },
  
  SPACING: {
    XS: '0.25rem',
    SM: '0.5rem',
    MD: '1rem',
    LG: '1.5rem',
    XL: '2rem',
    '2XL': '3rem',
    '3XL': '4rem',
  },
  
  TYPOGRAPHY: {
    FONT_FAMILY: {
      SANS: ['Inter', 'system-ui', 'sans-serif'],
      MONO: ['JetBrains Mono', 'Consolas', 'monospace'],
    },
    FONT_SIZES: {
      XS: '0.75rem',
      SM: '0.875rem',
      BASE: '1rem',
      LG: '1.125rem',
      XL: '1.25rem',
      '2XL': '1.5rem',
      '3XL': '1.875rem',
      '4XL': '2.25rem',
      '5XL': '3rem',
    },
  },
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  URL: /^https?:\/\/.+/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PASSWORD: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_URL: 'Please enter a valid URL',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit',
  INVALID_FILE_TYPE: 'Invalid file type',
  NETWORK_ERROR: 'Network error. Please try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  NOT_FOUND: 'The requested resource was not found',
  SERVER_ERROR: 'An unexpected error occurred. Please try again later.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN: 'Successfully logged in',
  LOGOUT: 'Successfully logged out',
  REGISTER: 'Account created successfully',
  PASSWORD_RESET: 'Password reset email sent',
  PROFILE_UPDATED: 'Profile updated successfully',
  POST_CREATED: 'Blog post created successfully',
  POST_UPDATED: 'Blog post updated successfully',
  POST_DELETED: 'Blog post deleted successfully',
  PROJECT_CREATED: 'Project created successfully',
  PROJECT_UPDATED: 'Project updated successfully',
  PROJECT_DELETED: 'Project deleted successfully',
  MESSAGE_SENT: 'Message sent successfully',
  NEWSLETTER_SUBSCRIBED: 'Successfully subscribed to newsletter',
  NEWSLETTER_UNSUBSCRIBED: 'Successfully unsubscribed from newsletter',
  SETTINGS_UPDATED: 'Settings updated successfully',
} as const;

// Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Cache Keys
export const CACHE_KEYS = {
  USER_SESSION: 'user_session',
  BLOG_POSTS: 'blog_posts',
  PROJECTS: 'projects',
  SITE_SETTINGS: 'site_settings',
  ANALYTICS: 'analytics',
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  AI_CONTENT_SUGGESTIONS: true,
  NEWSLETTER: true,
  ANALYTICS: true,
  DARK_MODE: true,
  COMMENTS: false, // Future feature
  SEARCH: true,
} as const;
