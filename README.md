# RxY.dev Platform

A production-ready fullstack web platform built to establish thought leadership and showcase fullstack engineering expertise. The platform consists of three independently deployed applications with a modern tech stack and comprehensive features.

## 🏗️ Architecture

### Three Decoupled Applications
- **Client App** (`apps/client`) - Public-facing website (rxy.dev)
- **Admin App** (`apps/admin`) - Content management dashboard (admin.rxy.dev)  
- **API Server** (`apps/api`) - Backend services (api.rxy.dev)

### Shared Packages
- **`@rxy/shared`** - Shared types, utilities, constants, and validation schemas
- **`@rxy/database`** - Database models and connection utilities
- **`@rxy/ui`** - Shared UI components (planned)

## 🚀 Tech Stack

### Frontend
- **Next.js 14+** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **Framer Motion** for animations
- **Zustand** for state management

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **MongoDB Atlas** with Mongoose
- **JWT** for authentication
- **Cloudinary** for media storage

### Development
- **Monorepo** structure with workspaces
- **ESLint** and **Prettier** for code quality
- **Jest** for testing
- **Docker** for containerization (planned)

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+ 
- npm 9+
- MongoDB Atlas account
- Cloudinary account (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rxy-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build shared packages**
   ```bash
   npm run build:shared
   ```

4. **Set up environment variables**
   
   Copy the example environment files and fill in your values:
   ```bash
   # API Server
   cp apps/api/.env.example apps/api/.env
   
   # Client App
   cp apps/client/.env.example apps/client/.env.local
   
   # Admin App (when created)
   cp apps/admin/.env.example apps/admin/.env.local
   ```

5. **Start development servers**
   ```bash
   # Start all applications
   npm run dev
   
   # Or start individually
   npm run dev:client   # Client on http://localhost:3000
   npm run dev:admin    # Admin on http://localhost:3001
   npm run dev:api      # API on http://localhost:3002
   ```

## 📁 Project Structure

```
rxy-platform/
├── apps/
│   ├── client/          # Next.js client website
│   ├── admin/           # Next.js admin dashboard
│   └── api/             # Express API server
├── packages/
│   ├── shared/          # Shared types, utilities, constants
│   ├── ui/              # Shared UI components
│   └── database/        # Database models and utilities
├── docs/                # Documentation
└── scripts/             # Build and deployment scripts
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start all apps in development
npm run dev:client       # Start client app only
npm run dev:admin        # Start admin app only
npm run dev:api          # Start API server only

# Building
npm run build            # Build all apps
npm run build:client     # Build client app
npm run build:admin      # Build admin app
npm run build:api        # Build API server
npm run build:shared     # Build shared packages

# Testing
npm run test             # Run all tests
npm run test:client      # Test client app
npm run test:admin       # Test admin app
npm run test:api         # Test API server

# Linting
npm run lint             # Lint all apps
npm run lint:fix         # Fix linting issues

# Utilities
npm run clean            # Clean all node_modules
npm run setup            # Install deps and build shared packages
```

### Environment Variables

#### API Server (`apps/api/.env`)
```env
# Database
MONGODB_URI=mongodb+srv://...

# JWT Secrets
JWT_ACCESS_SECRET=your-secret
JWT_REFRESH_SECRET=your-secret

# Email (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Cloudinary (optional)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# OpenAI (optional)
OPENAI_API_KEY=sk-your-key
```

#### Client App (`apps/client/.env.local`)
```env
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## 🚀 Deployment

### Production Deployment

1. **API Server** - Deploy to Railway, Fly.io, or similar
2. **Client App** - Deploy to Vercel with automatic deployments
3. **Admin App** - Deploy to Vercel with automatic deployments

### Environment Setup

Ensure all production environment variables are configured in your deployment platform.

## 📋 Features

### Phase 1: Foundation ✅
- [x] Project structure and monorepo setup
- [x] Shared packages with types and utilities
- [x] Database models and connection
- [x] API server with authentication
- [x] Basic client app structure

### Phase 2: Content Management (In Progress)
- [ ] Admin dashboard with authentication
- [ ] Blog management system
- [ ] Project portfolio management
- [ ] Contact form and message handling

### Phase 3: Advanced Features (Planned)
- [ ] OpenAI integration for content assistance
- [ ] Analytics and SEO optimization
- [ ] Newsletter functionality
- [ ] Social media management

### Phase 4: Polish & Deploy (Planned)
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Production deployment
- [ ] Documentation and maintenance

## 🤝 Contributing

This is a personal project, but suggestions and feedback are welcome!

## 📄 License

MIT License - see LICENSE file for details.

## 📞 Contact

- Website: [rxy.dev](https://rxy.dev)
- Email: <EMAIL>
- Twitter: [@rxydev](https://twitter.com/rxydev)
- LinkedIn: [rxydev](https://linkedin.com/in/rxydev)
