@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 47.4% 11.2%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
}

/* Custom Utilities */
@layer utilities {
  /* Grid Pattern Background */
  .bg-grid-pattern {
    background-image: linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .dark .bg-grid-pattern {
    background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }

  /* Line Clamp Utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Smooth Scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Professional Width Constraints */
  .container-professional {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-professional {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-professional {
      padding: 0 2rem;
    }
  }

  /* Focus Styles for Accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Custom Animations */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  /* Gradient Text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Selection styles */
::selection {
  @apply bg-primary/20 text-primary;
}

/* Typography improvements */
.prose {
  @apply max-w-none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  @apply font-semibold tracking-tight;
}

.prose h1 {
  @apply text-4xl lg:text-5xl;
}

.prose h2 {
  @apply text-3xl lg:text-4xl;
}

.prose h3 {
  @apply text-2xl lg:text-3xl;
}

.prose h4 {
  @apply text-xl lg:text-2xl;
}

.prose h5 {
  @apply text-lg lg:text-xl;
}

.prose h6 {
  @apply text-base lg:text-lg;
}

/* Code blocks */
.prose pre {
  @apply bg-muted border border-border rounded-lg p-4 overflow-x-auto;
}

.prose code {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono;
}

.prose pre code {
  @apply bg-transparent p-0;
}

/* Links */
.prose a {
  @apply text-primary hover:text-accent transition-colors duration-200 no-underline;
}

.prose a:hover {
  @apply underline;
}

/* Images */
.prose img {
  @apply rounded-lg shadow-sm;
}

/* Blockquotes */
.prose blockquote {
  @apply border-l-4 border-primary/20 pl-4 italic text-muted-foreground;
}

/* Tables */
.prose table {
  @apply w-full border-collapse border border-border;
}

.prose th,
.prose td {
  @apply border border-border px-4 py-2 text-left;
}

.prose th {
  @apply bg-muted font-semibold;
}

/* Lists */
.prose ul,
.prose ol {
  @apply space-y-1;
}

.prose li {
  @apply marker:text-muted-foreground;
}

/* Animations */
@keyframes gradient-x {
  0%,
  100% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(-100%);
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
  background-size: 200% 200%;
}

/* Loading states */
.loading-skeleton {
  @apply animate-pulse bg-muted rounded;
}

/* Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Container queries */
@container (min-width: 768px) {
  .container-md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}
