import { Router } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '@rxy/database';
import { config } from '@/config';
import { validateBody } from '@/middleware/validation';
import { authenticate } from '@/middleware/auth';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { 
  schemas, 
  HTTP_STATUS, 
  SUCCESS_MESSAGES, 
  ERROR_MESSAGES,
  generateId 
} from '@rxy/shared';

const router = Router();

// Register
router.post('/register', 
  validateBody(schemas.register),
  asyncHandler(async (req: any, res: any) => {
    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw createError('User already exists with this email', HTTP_STATUS.CONFLICT);
    }

    // Create new user
    const user = new User({
      name,
      email,
      password,
      role: 'user', // First user can be manually promoted to admin
    });

    await user.save();

    // Generate tokens
    const accessToken = user.generateAccessToken();
    const refreshToken = user.generateRefreshToken();
    
    // Save refresh token
    await user.addRefreshToken(refreshToken);

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message: SUCCESS_MESSAGES.REGISTER,
      data: {
        user: user.toJSON(),
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  })
);

// Login
router.post('/login',
  validateBody(schemas.login),
  asyncHandler(async (req: any, res: any) => {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      throw createError('Invalid credentials', HTTP_STATUS.UNAUTHORIZED);
    }

    // Check if account is locked
    if (user.isLocked()) {
      throw createError('Account is temporarily locked due to too many failed login attempts', HTTP_STATUS.UNAUTHORIZED);
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      await user.incrementLoginAttempts();
      throw createError('Invalid credentials', HTTP_STATUS.UNAUTHORIZED);
    }

    // Reset login attempts on successful login
    await user.resetLoginAttempts();

    // Update last login
    user.lastLoginAt = new Date();
    await user.save();

    // Generate tokens
    const accessToken = user.generateAccessToken();
    const refreshToken = user.generateRefreshToken();
    
    // Save refresh token
    await user.addRefreshToken(refreshToken);

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.LOGIN,
      data: {
        user: user.toJSON(),
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  })
);

// Refresh Token
router.post('/refresh',
  asyncHandler(async (req: any, res: any) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw createError('Refresh token is required', HTTP_STATUS.BAD_REQUEST);
    }

    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.JWT_REFRESH_SECRET) as any;
      
      // Find user and check if refresh token exists
      const user = await User.findById(decoded.id);
      if (!user || !user.refreshTokens.includes(refreshToken)) {
        throw createError('Invalid refresh token', HTTP_STATUS.UNAUTHORIZED);
      }

      // Generate new tokens
      const newAccessToken = user.generateAccessToken();
      const newRefreshToken = user.generateRefreshToken();

      // Replace old refresh token with new one
      await user.removeRefreshToken(refreshToken);
      await user.addRefreshToken(newRefreshToken);

      res.json({
        success: true,
        data: {
          tokens: {
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
          },
        },
      });
    } catch (error) {
      throw createError('Invalid refresh token', HTTP_STATUS.UNAUTHORIZED);
    }
  })
);

// Logout
router.post('/logout',
  authenticate,
  asyncHandler(async (req: any, res: any) => {
    const { refreshToken } = req.body;
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (user && refreshToken) {
      await user.removeRefreshToken(refreshToken);
    }

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.LOGOUT,
    });
  })
);

// Logout All (clear all refresh tokens)
router.post('/logout-all',
  authenticate,
  asyncHandler(async (req: any, res: any) => {
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (user) {
      await user.clearRefreshTokens();
    }

    res.json({
      success: true,
      message: 'Logged out from all devices',
    });
  })
);

// Get Current User
router.get('/me',
  authenticate,
  asyncHandler(async (req: any, res: any) => {
    const user = await User.findById(req.user.id);
    if (!user) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    res.json({
      success: true,
      data: { user: user.toJSON() },
    });
  })
);

// Forgot Password
router.post('/forgot-password',
  validateBody(schemas.forgotPassword),
  asyncHandler(async (req: any, res: any) => {
    const { email } = req.body;

    const user = await User.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not
      res.json({
        success: true,
        message: SUCCESS_MESSAGES.PASSWORD_RESET,
      });
      return;
    }

    // Generate reset token
    const resetToken = generateId(32);
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    await user.save();

    // TODO: Send email with reset link
    // await sendPasswordResetEmail(user.email, resetToken);

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.PASSWORD_RESET,
    });
  })
);

// Reset Password
router.post('/reset-password',
  validateBody(schemas.resetPassword),
  asyncHandler(async (req: any, res: any) => {
    const { token, password } = req.body;

    const user = await User.findByResetToken(token);
    if (!user) {
      throw createError('Invalid or expired reset token', HTTP_STATUS.BAD_REQUEST);
    }

    // Update password and clear reset token
    user.password = password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    
    // Clear all refresh tokens for security
    await user.clearRefreshTokens();
    
    await user.save();

    res.json({
      success: true,
      message: 'Password reset successfully',
    });
  })
);

export default router;
