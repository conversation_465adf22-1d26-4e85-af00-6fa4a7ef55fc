'use client';

import { useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

interface ShootingStar {
  id: number;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration: number;
  delay: number;
}

export function ShootingStarAnimation() {
  const [stars, setStars] = useState<ShootingStar[]>([]);
  const [mounted, setMounted] = useState(false);
  const { scrollY } = useScroll();
  
  // Parallax effect based on scroll
  const opacity = useTransform(scrollY, [0, 300], [1, 0.3]);

  useEffect(() => {
    setMounted(true);
    
    // Generate random shooting stars
    const generateStars = () => {
      const newStars: ShootingStar[] = [];
      for (let i = 0; i < 3; i++) {
        newStars.push({
          id: i,
          startX: Math.random() * 100,
          startY: Math.random() * 50,
          endX: Math.random() * 100,
          endY: 50 + Math.random() * 50,
          duration: 3 + Math.random() * 4, // 3-7 seconds
          delay: Math.random() * 10, // 0-10 seconds delay
        });
      }
      setStars(newStars);
    };

    generateStars();
    
    // Regenerate stars periodically
    const interval = setInterval(generateStars, 15000); // Every 15 seconds
    
    return () => clearInterval(interval);
  }, []);

  if (!mounted) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      <motion.div style={{ opacity }} className="relative w-full h-full">
        {stars.map((star) => (
          <motion.div
            key={star.id}
            className="absolute"
            initial={{
              x: `${star.startX}vw`,
              y: `${star.startY}vh`,
              opacity: 0,
            }}
            animate={{
              x: `${star.endX}vw`,
              y: `${star.endY}vh`,
              opacity: [0, 1, 1, 0],
            }}
            transition={{
              duration: star.duration,
              delay: star.delay,
              repeat: Infinity,
              repeatDelay: 15,
              ease: "easeInOut",
            }}
          >
            {/* Shooting Star SVG */}
            <svg
              width="60"
              height="4"
              viewBox="0 0 60 4"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="transform rotate-45"
            >
              <defs>
                <linearGradient id={`gradient-${star.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="transparent" />
                  <stop offset="20%" stopColor="rgba(59, 130, 246, 0.3)" />
                  <stop offset="80%" stopColor="rgba(59, 130, 246, 0.8)" />
                  <stop offset="100%" stopColor="rgba(255, 255, 255, 1)" />
                </linearGradient>
              </defs>
              <rect
                width="60"
                height="2"
                y="1"
                fill={`url(#gradient-${star.id})`}
                rx="1"
              />
            </svg>
            
            {/* Glow Effect */}
            <div className="absolute top-1/2 right-0 w-2 h-2 bg-white rounded-full transform -translate-y-1/2 shadow-lg">
              <div className="absolute inset-0 bg-blue-400 rounded-full animate-pulse opacity-60" />
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}

// Optimized version using CSS animations for better performance
export function OptimizedShootingStars() {
  const [mounted, setMounted] = useState(false);
  const { scrollY } = useScroll();
  const opacity = useTransform(scrollY, [0, 300], [1, 0.3]);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <motion.div 
      style={{ opacity }}
      className="fixed inset-0 pointer-events-none overflow-hidden z-0"
    >
      {/* CSS-based shooting stars for better performance */}
      <div className="shooting-stars">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="shooting-star"
            style={{
              '--delay': `${i * 3}s`,
              '--duration': `${4 + Math.random() * 2}s`,
              '--start-x': `${Math.random() * 100}%`,
              '--start-y': `${Math.random() * 50}%`,
              '--end-x': `${Math.random() * 100}%`,
              '--end-y': `${50 + Math.random() * 50}%`,
            } as React.CSSProperties}
          />
        ))}
      </div>
    </motion.div>
  );
}
