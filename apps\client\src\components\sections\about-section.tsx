'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Calendar, MapPin, Award, TrendingUp } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const timeline = [
  {
    year: '2024',
    title: 'Senior Fullstack Developer',
    company: 'Tech Innovation Corp',
    location: 'Remote',
    description: 'Leading development of enterprise-scale applications serving 100K+ users. Architecting microservices and implementing CI/CD pipelines.',
    achievements: ['Reduced deployment time by 70%', 'Led team of 5 developers', 'Implemented real-time analytics'],
    current: true,
  },
  {
    year: '2022',
    title: 'Fullstack Developer',
    company: 'Digital Solutions Ltd',
    location: 'New York, NY',
    description: 'Built responsive web applications using React, Node.js, and MongoDB. Collaborated with design teams to create pixel-perfect UIs.',
    achievements: ['Delivered 15+ projects on time', 'Improved app performance by 40%', 'Mentored junior developers'],
  },
  {
    year: '2020',
    title: 'Frontend Developer',
    company: 'StartupXYZ',
    location: 'San Francisco, CA',
    description: 'Developed user interfaces for SaaS platform. Focused on creating intuitive user experiences and optimizing for mobile devices.',
    achievements: ['Increased user engagement by 60%', 'Built component library', 'Implemented A/B testing'],
  },
  {
    year: '2019',
    title: 'Computer Science Graduate',
    company: 'University of Technology',
    location: 'California',
    description: 'Graduated with honors, specializing in software engineering and web technologies. Completed capstone project on AI-powered web applications.',
    achievements: ['Summa Cum Laude', 'Dean\'s List 4 semesters', 'Published research paper'],
  },
];

const skills = [
  { category: 'Frontend', items: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'] },
  { category: 'Backend', items: ['Node.js', 'Express', 'Python', 'PostgreSQL', 'MongoDB'] },
  { category: 'Cloud & DevOps', items: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Terraform'] },
  { category: 'Tools & Others', items: ['Git', 'Figma', 'Jest', 'Cypress', 'Agile/Scrum'] },
];

export function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section id="about" className="py-20 lg:py-32 bg-muted/30" ref={ref}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge variant="secondary" className="mb-4">
            About Me
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Crafting Digital Excellence
            <span className="block text-primary">Since 2019</span>
          </h2>
          <p className="max-w-3xl mx-auto text-lg text-muted-foreground leading-relaxed">
            I'm a passionate fullstack developer who transforms complex business requirements 
            into elegant, scalable solutions. With a focus on user experience and performance, 
            I help businesses achieve their digital goals.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Timeline */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h3 className="text-2xl font-bold text-foreground mb-8 flex items-center">
              <Calendar className="w-6 h-6 mr-3 text-primary" />
              Professional Journey
            </h3>
            
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-border" />
              
              {timeline.map((item, index) => (
                <motion.div
                  key={item.year}
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                  className="relative flex items-start mb-12 last:mb-0"
                >
                  {/* Timeline Dot */}
                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-4 border-background ${
                    item.current ? 'bg-primary' : 'bg-muted'
                  }`}>
                    {item.current ? (
                      <TrendingUp className="w-5 h-5 text-primary-foreground" />
                    ) : (
                      <Award className="w-5 h-5 text-muted-foreground" />
                    )}
                  </div>
                  
                  {/* Content */}
                  <div className="ml-6 flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-semibold text-primary bg-primary/10 px-2 py-1 rounded">
                        {item.year}
                      </span>
                      {item.current && (
                        <Badge variant="default" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                    
                    <h4 className="text-xl font-bold text-foreground mb-1">
                      {item.title}
                    </h4>
                    
                    <div className="flex items-center text-muted-foreground mb-3">
                      <span className="font-medium">{item.company}</span>
                      <span className="mx-2">•</span>
                      <MapPin className="w-4 h-4 mr-1" />
                      <span>{item.location}</span>
                    </div>
                    
                    <p className="text-muted-foreground mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    
                    <div className="space-y-1">
                      {item.achievements.map((achievement, i) => (
                        <div key={i} className="flex items-center text-sm text-muted-foreground">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2" />
                          {achievement}
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Skills */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h3 className="text-2xl font-bold text-foreground mb-8">
              Technical Expertise
            </h3>
            
            <div className="space-y-8">
              {skills.map((skillGroup, index) => (
                <motion.div
                  key={skillGroup.category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  className="bg-background rounded-xl p-6 border border-border shadow-sm hover:shadow-md transition-shadow"
                >
                  <h4 className="text-lg font-semibold text-foreground mb-4">
                    {skillGroup.category}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {skillGroup.items.map((skill) => (
                      <Badge
                        key={skill}
                        variant="secondary"
                        className="bg-muted/50 text-muted-foreground hover:bg-muted transition-colors"
                      >
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Personal Touch */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="mt-8 p-6 bg-primary/5 rounded-xl border border-primary/20"
            >
              <h4 className="text-lg font-semibold text-foreground mb-3">
                Beyond Code
              </h4>
              <p className="text-muted-foreground leading-relaxed">
                When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, 
                or sharing knowledge through technical writing. I believe in continuous learning and giving back to the developer community.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
