{"name": "@rxy/database", "version": "1.0.0", "description": "Database models and utilities for RxY platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@rxy/shared": "^1.0.0", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "rimraf": "^5.0.5", "typescript": "^5.3.0"}}