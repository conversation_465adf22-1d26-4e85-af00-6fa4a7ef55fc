'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { ExternalLink, Github, Filter, Zap, Users, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const categories = ['All', 'Web Apps', 'E-commerce', 'SaaS', 'Mobile'];

const projects = [
  {
    id: 1,
    title: 'EcoCommerce Platform',
    description: 'A sustainable e-commerce platform with AI-powered product recommendations and carbon footprint tracking.',
    category: 'E-commerce',
    image: '/images/projects/ecommerce-platform.jpg', // Admin will upload
    technologies: ['Next.js', 'TypeScript', 'Stripe', 'MongoDB', 'AI/ML'],
    features: ['Real-time inventory', 'AI recommendations', 'Carbon tracking', 'Multi-vendor support'],
    metrics: { users: '50K+', conversion: '12%', performance: '98%' },
    links: {
      live: 'https://ecocommerce-demo.com',
      github: 'https://github.com/rxydev/ecommerce-platform',
    },
    status: 'Live',
    featured: true,
  },
  {
    id: 2,
    title: 'TaskFlow SaaS',
    description: 'A comprehensive project management tool with real-time collaboration and advanced analytics.',
    category: 'SaaS',
    image: '/images/projects/taskflow-saas.jpg', // Admin will upload
    technologies: ['React', 'Node.js', 'PostgreSQL', 'Socket.io', 'AWS'],
    features: ['Real-time collaboration', 'Advanced analytics', 'Team management', 'API integrations'],
    metrics: { users: '25K+', retention: '85%', uptime: '99.9%' },
    links: {
      live: 'https://taskflow-demo.com',
      github: 'https://github.com/rxydev/taskflow-saas',
    },
    status: 'Live',
    featured: true,
  },
  {
    id: 3,
    title: 'HealthTech Dashboard',
    description: 'A medical dashboard for healthcare providers with patient management and telemedicine features.',
    category: 'Web Apps',
    image: '/images/projects/healthtech-dashboard.jpg', // Admin will upload
    technologies: ['Vue.js', 'Express', 'MongoDB', 'WebRTC', 'HIPAA'],
    features: ['Patient records', 'Video consultations', 'Appointment scheduling', 'HIPAA compliant'],
    metrics: { providers: '500+', consultations: '10K+', satisfaction: '96%' },
    links: {
      live: 'https://healthtech-demo.com',
      github: null, // Private repository
    },
    status: 'Live',
    featured: false,
  },
  {
    id: 4,
    title: 'CryptoTracker Mobile',
    description: 'A React Native app for cryptocurrency portfolio tracking with real-time price alerts.',
    category: 'Mobile',
    image: '/images/projects/crypto-tracker.jpg', // Admin will upload
    technologies: ['React Native', 'Redux', 'Firebase', 'CoinGecko API'],
    features: ['Portfolio tracking', 'Price alerts', 'News feed', 'Offline support'],
    metrics: { downloads: '100K+', rating: '4.8/5', retention: '70%' },
    links: {
      live: 'https://apps.apple.com/cryptotracker',
      github: 'https://github.com/rxydev/crypto-tracker',
    },
    status: 'Live',
    featured: false,
  },
  {
    id: 5,
    title: 'AI Content Generator',
    description: 'An AI-powered content creation platform for marketers and content creators.',
    category: 'SaaS',
    image: '/images/projects/ai-content-generator.jpg', // Admin will upload
    technologies: ['Next.js', 'OpenAI API', 'Prisma', 'Stripe', 'Vercel'],
    features: ['AI content generation', 'Template library', 'Team collaboration', 'Usage analytics'],
    metrics: { users: '15K+', content: '1M+', satisfaction: '94%' },
    links: {
      live: 'https://ai-content-demo.com',
      github: 'https://github.com/rxydev/ai-content-generator',
    },
    status: 'Beta',
    featured: true,
  },
  {
    id: 6,
    title: 'Restaurant POS System',
    description: 'A complete point-of-sale system for restaurants with inventory management and analytics.',
    category: 'Web Apps',
    image: '/images/projects/restaurant-pos.jpg', // Admin will upload
    technologies: ['React', 'Node.js', 'MySQL', 'Socket.io', 'Electron'],
    features: ['Order management', 'Inventory tracking', 'Staff management', 'Sales analytics'],
    metrics: { restaurants: '200+', orders: '500K+', efficiency: '+40%' },
    links: {
      live: 'https://restaurant-pos-demo.com',
      github: null, // Private repository
    },
    status: 'Live',
    featured: false,
  },
];

export function ProjectsSection() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showAll, setShowAll] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const filteredProjects = projects.filter(project => 
    selectedCategory === 'All' || project.category === selectedCategory
  );

  const displayedProjects = showAll ? filteredProjects : filteredProjects.slice(0, 6);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Live': return 'bg-green-100 text-green-800 border-green-200';
      case 'Beta': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Development': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <section id="projects" className="py-20 lg:py-32 bg-background" ref={ref}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge variant="secondary" className="mb-4">
            Portfolio
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Featured Projects
            <span className="block text-primary">That Drive Results</span>
          </h2>
          <p className="max-w-3xl mx-auto text-lg text-muted-foreground leading-relaxed">
            Explore a selection of my recent work, showcasing innovative solutions 
            that have helped businesses achieve their goals and exceed user expectations.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-2 mb-12"
        >
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              className="px-6 py-2"
            >
              <Filter className="w-4 h-4 mr-2" />
              {category}
            </Button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {displayedProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
            >
              <Card className="group h-full hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 overflow-hidden">
                {/* Project Image */}
                <div className="relative overflow-hidden bg-muted/30 h-48">
                  {/* Placeholder for admin-uploaded image */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <Zap className="w-8 h-8 text-primary" />
                      </div>
                      <p className="text-sm text-muted-foreground font-medium">
                        {project.title}
                      </p>
                    </div>
                  </div>
                  
                  {/* Status Badge */}
                  <div className="absolute top-4 left-4">
                    <Badge className={getStatusColor(project.status)}>
                      {project.status}
                    </Badge>
                  </div>
                  
                  {/* Featured Badge */}
                  {project.featured && (
                    <div className="absolute top-4 right-4">
                      <Badge variant="default" className="bg-accent text-accent-foreground">
                        Featured
                      </Badge>
                    </div>
                  )}
                </div>

                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                        {project.title}
                      </CardTitle>
                      <CardDescription className="text-muted-foreground mt-2 leading-relaxed">
                        {project.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Technologies */}
                  <div className="flex flex-wrap gap-1">
                    {project.technologies.slice(0, 4).map((tech) => (
                      <Badge key={tech} variant="secondary" className="text-xs">
                        {tech}
                      </Badge>
                    ))}
                    {project.technologies.length > 4 && (
                      <Badge variant="secondary" className="text-xs">
                        +{project.technologies.length - 4}
                      </Badge>
                    )}
                  </div>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-3 gap-2 text-center">
                    {Object.entries(project.metrics).map(([key, value]) => (
                      <div key={key} className="bg-muted/30 rounded-lg p-2">
                        <div className="text-sm font-bold text-foreground">{value}</div>
                        <div className="text-xs text-muted-foreground capitalize">{key}</div>
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="default"
                      size="sm"
                      className="flex-1"
                      asChild
                    >
                      <a
                        href={project.links.live}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Live Demo
                      </a>
                    </Button>
                    
                    {project.links.github && (
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                      >
                        <a
                          href={project.links.github}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Github className="w-4 h-4" />
                        </a>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Show More Button */}
        {filteredProjects.length > 6 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="text-center"
          >
            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowAll(!showAll)}
              className="px-8"
            >
              {showAll ? 'Show Less' : `View All ${filteredProjects.length} Projects`}
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  );
}
